using System;
using System.IO;
using System.Threading.Tasks;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 简单的工具调用兼容性测试
    /// </summary>
    public class SimpleToolTest
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("🧪 简单工具调用兼容性测试");
            Console.WriteLine(new string('=', 50));

            try
            {
                // 测试基本功能
                await TestBasicFunctionality();
                
                // 测试项目分析
                await TestProjectAnalysis();
                
                Console.WriteLine("✅ 测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        private static async Task TestBasicFunctionality()
        {
            Console.WriteLine("📋 测试基本功能...");
            
            // 测试目录扫描
            var currentDir = Directory.GetCurrentDirectory();
            Console.WriteLine($"当前目录: {currentDir}");
            
            var files = Directory.GetFiles(currentDir, "*.*", SearchOption.TopDirectoryOnly);
            Console.WriteLine($"文件数量: {files.Length}");
            
            var directories = Directory.GetDirectories(currentDir, "*", SearchOption.TopDirectoryOnly);
            Console.WriteLine($"目录数量: {directories.Length}");
            
            Console.WriteLine("✅ 基本功能测试完成");
            Console.WriteLine();
        }

        private static async Task TestProjectAnalysis()
        {
            Console.WriteLine("📊 测试项目分析...");
            
            // 查找可能的项目路径
            var possiblePaths = new[]
            {
                @"H:\AI创作系统\诡异收藏家",
                @".\诡异收藏家",
                @".\Projects\诡异收藏家",
                @"DocumentCreationSystem\Projects\Project_3",
                @"DocumentCreationSystem\Projects\Project_4",
                @"Projects\Project_3",
                @"Projects\Project_4"
            };

            string foundPath = null;
            foreach (var path in possiblePaths)
            {
                if (Directory.Exists(path))
                {
                    foundPath = path;
                    Console.WriteLine($"✅ 找到项目路径: {path}");
                    break;
                }
                else
                {
                    Console.WriteLine($"❌ 路径不存在: {path}");
                }
            }

            if (foundPath != null)
            {
                // 分析找到的项目
                var files = Directory.GetFiles(foundPath, "*.*", SearchOption.AllDirectories);
                var directories = Directory.GetDirectories(foundPath, "*", SearchOption.AllDirectories);
                
                Console.WriteLine($"项目文件数: {files.Length}");
                Console.WriteLine($"项目目录数: {directories.Length}");
                
                // 显示前几个文件
                Console.WriteLine("主要文件:");
                for (int i = 0; i < Math.Min(5, files.Length); i++)
                {
                    var fileName = Path.GetFileName(files[i]);
                    var fileSize = new FileInfo(files[i]).Length;
                    Console.WriteLine($"  📄 {fileName} ({FormatFileSize(fileSize)})");
                }
            }
            else
            {
                Console.WriteLine("❌ 未找到任何项目路径");
            }
            
            Console.WriteLine("✅ 项目分析测试完成");
            Console.WriteLine();
        }

        private static string FormatFileSize(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024 * 1024):F1} MB";
            return $"{bytes / (1024 * 1024 * 1024):F1} GB";
        }
    }
}

/// <summary>
/// 使用说明：
/// 
/// 1. 运行测试：
///    dotnet run --project DocumentCreationSystem SimpleToolTest.cs
/// 
/// 2. 功能说明：
///    - 测试基本的文件系统访问功能
///    - 查找和分析可能的项目路径
///    - 验证目录扫描和文件统计功能
/// 
/// 3. 这个简化测试用于验证：
///    - 基本的.NET功能是否正常
///    - 文件系统访问是否有权限问题
///    - 项目路径是否存在
///    - 为完整的工具调用兼容性服务提供基础验证
/// </summary>
