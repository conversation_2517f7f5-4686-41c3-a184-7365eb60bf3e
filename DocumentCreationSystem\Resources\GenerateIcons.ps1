# PowerShell脚本：生成应用程序图标
# 需要安装Inkscape或其他SVG转换工具

param(
    [string]$InkscapePath = "C:\Program Files\Inkscape\bin\inkscape.exe"
)

Write-Host "开始生成应用程序图标..." -ForegroundColor Green

# 检查Inkscape是否存在
if (-not (Test-Path $InkscapePath)) {
    Write-Host "未找到Inkscape，请安装Inkscape或指定正确路径" -ForegroundColor Red
    Write-Host "下载地址: https://inkscape.org/release/" -ForegroundColor Yellow
    Write-Host "或者使用在线SVG转换工具" -ForegroundColor Yellow
    exit 1
}

# 创建输出目录
$outputDir = "Generated"
if (-not (Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir | Out-Null
}

# 定义需要生成的尺寸
$sizes = @(16, 24, 32, 48, 64, 96, 128, 256)

Write-Host "生成PNG图标文件..." -ForegroundColor Cyan

# 从主图标生成不同尺寸的PNG
foreach ($size in $sizes) {
    $outputFile = "$outputDir\AppIcon_$size.png"
    Write-Host "生成 ${size}x${size} PNG..." -ForegroundColor Gray
    
    & $InkscapePath --export-type=png --export-filename=$outputFile --export-width=$size --export-height=$size "AppIcon.svg"
    
    if (Test-Path $outputFile) {
        Write-Host "✓ 生成成功: $outputFile" -ForegroundColor Green
    } else {
        Write-Host "✗ 生成失败: $outputFile" -ForegroundColor Red
    }
}

# 从简化图标生成小尺寸PNG
foreach ($size in @(16, 24, 32)) {
    $outputFile = "$outputDir\AppIcon_Simple_$size.png"
    Write-Host "生成简化版 ${size}x${size} PNG..." -ForegroundColor Gray
    
    & $InkscapePath --export-type=png --export-filename=$outputFile --export-width=$size --export-height=$size "AppIcon_Simple.svg"
    
    if (Test-Path $outputFile) {
        Write-Host "✓ 生成成功: $outputFile" -ForegroundColor Green
    } else {
        Write-Host "✗ 生成失败: $outputFile" -ForegroundColor Red
    }
}

Write-Host "`n图标生成完成！" -ForegroundColor Green
Write-Host "生成的文件位于: $outputDir 目录" -ForegroundColor Yellow

# 生成使用说明
$readme = @"
# 应用程序图标使用说明

## 生成的文件

### PNG图标文件
- AppIcon_16.png - 16x16 像素，用于任务栏小图标
- AppIcon_24.png - 24x24 像素，用于小图标显示
- AppIcon_32.png - 32x32 像素，用于标准图标
- AppIcon_48.png - 48x48 像素，用于中等图标
- AppIcon_64.png - 64x64 像素，用于大图标
- AppIcon_96.png - 96x96 像素，用于高DPI显示
- AppIcon_128.png - 128x128 像素，用于高分辨率
- AppIcon_256.png - 256x256 像素，用于超高分辨率

### 简化版图标（用于小尺寸显示）
- AppIcon_Simple_16.png - 16x16 像素简化版
- AppIcon_Simple_24.png - 24x24 像素简化版
- AppIcon_Simple_32.png - 32x32 像素简化版

## 在WPF项目中使用

### 1. 设置应用程序图标
在项目文件(.csproj)中添加：
```xml
<PropertyGroup>
    <ApplicationIcon>Resources\AppIcon.ico</ApplicationIcon>
</PropertyGroup>
```

### 2. 设置窗口图标
在XAML中：
```xml
<Window Icon="Resources/AppIcon_32.png">
```

### 3. 在代码中使用
```csharp
this.Icon = new BitmapImage(new Uri("pack://application:,,,/Resources/AppIcon_32.png"));
```

## 创建ICO文件

使用在线工具或专业软件将PNG文件合并为ICO文件：
1. 访问 https://www.icoconverter.com/
2. 上传 AppIcon_256.png
3. 选择包含多个尺寸 (16, 24, 32, 48, 64, 128, 256)
4. 下载生成的 AppIcon.ico 文件

## 图标设计说明

### 主要元素
- **文档图标**: 代表文档创建和管理功能
- **AI标识**: 体现人工智能辅助功能
- **智能笔**: 象征智能创作和编辑
- **管理齿轮**: 表示系统管理功能
- **项目文件夹**: 代表项目组织功能

### 颜色方案
- **主色调**: 蓝色渐变 (#4A90E2 到 #357ABD) - 专业、可靠
- **AI色调**: 紫色渐变 (#7B68EE 到 #6A5ACD) - 智能、创新
- **创作色调**: 红色渐变 (#FF6B6B 到 #E55555) - 活力、创造
- **辅助色调**: 金色 (#FFD700) - 灵感、价值

### 适用场景
- Windows应用程序图标
- 任务栏图标
- 开始菜单图标
- 桌面快捷方式图标
- 文件关联图标
"@

$readme | Out-File -FilePath "$outputDir\README.md" -Encoding UTF8

Write-Host "`n使用说明已生成: $outputDir\README.md" -ForegroundColor Cyan
Write-Host "`n下一步操作:" -ForegroundColor Yellow
Write-Host "1. 使用在线工具将PNG转换为ICO文件" -ForegroundColor White
Write-Host "2. 将图标文件复制到项目的Resources目录" -ForegroundColor White
Write-Host "3. 在项目文件中设置ApplicationIcon属性" -ForegroundColor White
