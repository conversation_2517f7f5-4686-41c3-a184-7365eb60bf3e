@echo off
chcp 65001 >nul
echo 文档创建管理系统 - 基本图标生成器
echo =====================================
echo.

REM 检查是否存在.NET SDK
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到.NET SDK
    echo 请安装.NET 8.0 SDK: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo 正在编译图标生成器...

REM 创建临时项目文件
echo ^<Project Sdk="Microsoft.NET.Sdk"^> > IconGenerator.csproj
echo   ^<PropertyGroup^> >> IconGenerator.csproj
echo     ^<OutputType^>Exe^</OutputType^> >> IconGenerator.csproj
echo     ^<TargetFramework^>net8.0-windows^</TargetFramework^> >> IconGenerator.csproj
echo     ^<UseWindowsForms^>true^</UseWindowsForms^> >> IconGenerator.csproj
echo     ^<Nullable^>enable^</Nullable^> >> IconGenerator.csproj
echo   ^</PropertyGroup^> >> IconGenerator.csproj
echo ^</Project^> >> IconGenerator.csproj

REM 编译并运行
echo 正在生成图标...
dotnet run --project IconGenerator.csproj CreateBasicIcon.cs

REM 清理临时文件
if exist IconGenerator.csproj del IconGenerator.csproj
if exist bin rmdir /s /q bin
if exist obj rmdir /s /q obj

echo.
echo 图标生成完成！
echo 生成的文件位于 Generated 目录中
echo.

REM 检查生成结果
if exist "Generated\AppIcon.ico" (
    echo ✓ AppIcon.ico - 主应用程序图标
) else (
    echo ✗ AppIcon.ico 生成失败
)

if exist "Generated\AppIcon_32.png" (
    echo ✓ PNG图标文件已生成
) else (
    echo ✗ PNG图标文件生成失败
)

echo.
echo 使用说明:
echo 1. 将 Generated\AppIcon.ico 复制到项目的 Resources 目录
echo 2. 在项目文件中设置 ^<ApplicationIcon^>Resources\AppIcon.ico^</ApplicationIcon^>
echo 3. 重新编译项目即可看到新图标
echo.
pause
