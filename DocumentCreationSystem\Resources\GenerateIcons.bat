@echo off
chcp 65001 >nul
echo 开始生成应用程序图标...

REM 检查是否存在Inkscape
set INKSCAPE_PATH="C:\Program Files\Inkscape\bin\inkscape.exe"
if not exist %INKSCAPE_PATH% (
    echo 未找到Inkscape，请安装Inkscape
    echo 下载地址: https://inkscape.org/release/
    echo.
    echo 或者使用在线SVG转换工具:
    echo 1. 访问 https://www.icoconverter.com/
    echo 2. 上传 AppIcon.svg 文件
    echo 3. 选择生成多尺寸ICO文件
    echo 4. 下载并重命名为 AppIcon.ico
    pause
    exit /b 1
)

REM 创建输出目录
if not exist "Generated" mkdir "Generated"

echo 生成PNG图标文件...

REM 生成不同尺寸的PNG文件
%INKSCAPE_PATH% --export-type=png --export-filename=Generated\AppIcon_16.png --export-width=16 --export-height=16 AppIcon.svg
%INKSCAPE_PATH% --export-type=png --export-filename=Generated\AppIcon_24.png --export-width=24 --export-height=24 AppIcon.svg
%INKSCAPE_PATH% --export-type=png --export-filename=Generated\AppIcon_32.png --export-width=32 --export-height=32 AppIcon.svg
%INKSCAPE_PATH% --export-type=png --export-filename=Generated\AppIcon_48.png --export-width=48 --export-height=48 AppIcon.svg
%INKSCAPE_PATH% --export-type=png --export-filename=Generated\AppIcon_64.png --export-width=64 --export-height=64 AppIcon.svg
%INKSCAPE_PATH% --export-type=png --export-filename=Generated\AppIcon_128.png --export-width=128 --export-height=128 AppIcon.svg
%INKSCAPE_PATH% --export-type=png --export-filename=Generated\AppIcon_256.png --export-width=256 --export-height=256 AppIcon.svg

REM 生成简化版小尺寸图标
%INKSCAPE_PATH% --export-type=png --export-filename=Generated\AppIcon_Simple_16.png --export-width=16 --export-height=16 AppIcon_Simple.svg
%INKSCAPE_PATH% --export-type=png --export-filename=Generated\AppIcon_Simple_24.png --export-width=24 --export-height=24 AppIcon_Simple.svg
%INKSCAPE_PATH% --export-type=png --export-filename=Generated\AppIcon_Simple_32.png --export-width=32 --export-height=32 AppIcon_Simple.svg

echo.
echo 图标生成完成！
echo 生成的文件位于: Generated 目录
echo.
echo 下一步操作:
echo 1. 使用在线工具将PNG转换为ICO文件
echo 2. 推荐使用 AppIcon_256.png 生成ICO文件
echo 3. 将ICO文件重命名为 AppIcon.ico
echo 4. 复制到项目的Resources目录
echo.
pause
