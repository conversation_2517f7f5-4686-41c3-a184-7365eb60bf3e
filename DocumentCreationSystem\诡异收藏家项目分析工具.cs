using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 诡异收藏家项目分析工具
    /// 用于分析项目当前状态并提供完善建议
    /// </summary>
    public class ProjectAnalyzer
    {
        private readonly string _projectPath;
        
        public ProjectAnalyzer(string projectPath = @"H:\AI创作系统\诡异收藏家")
        {
            _projectPath = projectPath;
        }

        /// <summary>
        /// 分析项目结构和内容
        /// </summary>
        public async Task<ProjectAnalysisResult> AnalyzeProjectAsync()
        {
            var result = new ProjectAnalysisResult
            {
                ProjectPath = _projectPath,
                AnalysisTime = DateTime.Now
            };

            if (!Directory.Exists(_projectPath))
            {
                result.Issues.Add($"项目路径不存在: {_projectPath}");
                return result;
            }

            // 分析文件夹结构
            await AnalyzeFolderStructureAsync(result);
            
            // 分析章节文件
            await AnalyzeChaptersAsync(result);
            
            // 分析大纲文件
            await AnalyzeOutlinesAsync(result);
            
            // 分析设定文件
            await AnalyzeSettingsAsync(result);
            
            // 生成完善建议
            GenerateImprovementSuggestions(result);

            return result;
        }

        private async Task AnalyzeFolderStructureAsync(ProjectAnalysisResult result)
        {
            var expectedFolders = new[] { "章节", "大纲", "设定", "角色", "文档", "资源", "备份", "导出" };
            
            foreach (var folder in expectedFolders)
            {
                var folderPath = Path.Combine(_projectPath, folder);
                if (Directory.Exists(folderPath))
                {
                    result.ExistingFolders.Add(folder);
                    var fileCount = Directory.GetFiles(folderPath, "*", SearchOption.AllDirectories).Length;
                    result.FolderFileCounts[folder] = fileCount;
                }
                else
                {
                    result.MissingFolders.Add(folder);
                }
            }
        }

        private async Task AnalyzeChaptersAsync(ProjectAnalysisResult result)
        {
            var chaptersPath = Path.Combine(_projectPath, "章节");
            if (!Directory.Exists(chaptersPath))
            {
                result.Issues.Add("章节文件夹不存在");
                return;
            }

            var chapterFiles = Directory.GetFiles(chaptersPath, "*.txt", SearchOption.TopDirectoryOnly);
            result.ChapterCount = chapterFiles.Length;

            foreach (var file in chapterFiles)
            {
                var content = await File.ReadAllTextAsync(file);
                var wordCount = content.Length;
                var fileName = Path.GetFileNameWithoutExtension(file);
                
                result.ChapterDetails.Add(new ChapterInfo
                {
                    FileName = fileName,
                    WordCount = wordCount,
                    FilePath = file,
                    IsComplete = wordCount > 1000, // 简单判断
                    HasIssues = content.Contains("AI思维链") || content.Contains("错误信息")
                });
            }
        }

        private async Task AnalyzeOutlinesAsync(ProjectAnalysisResult result)
        {
            var outlinesPath = Path.Combine(_projectPath, "大纲");
            if (!Directory.Exists(outlinesPath))
            {
                result.Issues.Add("大纲文件夹不存在");
                return;
            }

            // 检查全书大纲
            var overallOutlineFiles = Directory.GetFiles(outlinesPath, "*overall*.txt", SearchOption.TopDirectoryOnly);
            result.HasOverallOutline = overallOutlineFiles.Length > 0;

            // 检查章节细纲（新的文件夹结构：直接在大纲文件夹下）
            var chapterOutlineFiles = Directory.GetFiles(outlinesPath, "*章节细纲*.txt", SearchOption.TopDirectoryOnly);
            result.ChapterOutlineCount = chapterOutlineFiles.Length;

            // 兼容旧的文件夹结构
            var chapterOutlinesPath = Path.Combine(outlinesPath, "章节");
            if (Directory.Exists(chapterOutlinesPath))
            {
                var oldChapterOutlineFiles = Directory.GetFiles(chapterOutlinesPath, "*.txt", SearchOption.TopDirectoryOnly);
                result.ChapterOutlineCount += oldChapterOutlineFiles.Length;
            }

            var chapterOutlinesPath2 = Path.Combine(outlinesPath, "章节细纲");
            if (Directory.Exists(chapterOutlinesPath2))
            {
                var oldChapterOutlineFiles2 = Directory.GetFiles(chapterOutlinesPath2, "*.txt", SearchOption.TopDirectoryOnly);
                result.ChapterOutlineCount += oldChapterOutlineFiles2.Length;
            }

            // 检查分卷大纲（新的文件夹结构：直接在大纲文件夹下）
            var volumeOutlineFiles = Directory.GetFiles(outlinesPath, "*分卷大纲*.txt", SearchOption.TopDirectoryOnly);
            result.VolumeOutlineCount = volumeOutlineFiles.Length;

            // 兼容旧的文件夹结构
            var volumeOutlinesPath = Path.Combine(outlinesPath, "分卷");
            if (Directory.Exists(volumeOutlinesPath))
            {
                var oldVolumeOutlineFiles = Directory.GetFiles(volumeOutlinesPath, "*.txt", SearchOption.TopDirectoryOnly);
                result.VolumeOutlineCount += oldVolumeOutlineFiles.Length;
            }

            var volumeOutlinesPath2 = Path.Combine(outlinesPath, "分卷大纲");
            if (Directory.Exists(volumeOutlinesPath2))
            {
                var oldVolumeOutlineFiles2 = Directory.GetFiles(volumeOutlinesPath2, "*.txt", SearchOption.TopDirectoryOnly);
                result.VolumeOutlineCount += oldVolumeOutlineFiles2.Length;
            }
        }

        private async Task AnalyzeSettingsAsync(ProjectAnalysisResult result)
        {
            var settingsPath = Path.Combine(_projectPath, "设定");
            if (!Directory.Exists(settingsPath))
            {
                result.Issues.Add("设定文件夹不存在");
                return;
            }

            var expectedSettings = new[]
            {
                "世界观设定管理.md", "角色设定管理.md", "修炼体系设定管理.md",
                "时间线管理.md", "势力管理.md", "地图结构管理.md"
            };

            foreach (var setting in expectedSettings)
            {
                var settingPath = Path.Combine(settingsPath, setting);
                if (File.Exists(settingPath))
                {
                    result.ExistingSettings.Add(setting);
                }
                else
                {
                    result.MissingSettings.Add(setting);
                }
            }
        }

        private void GenerateImprovementSuggestions(ProjectAnalysisResult result)
        {
            // 基于分析结果生成建议
            if (result.ChapterCount == 0)
            {
                result.Suggestions.Add("建议使用一键写书功能开始创作章节内容");
            }
            else if (result.ChapterCount < 10)
            {
                result.Suggestions.Add($"当前有{result.ChapterCount}章，建议继续创作更多章节");
            }

            if (!result.HasOverallOutline)
            {
                result.Suggestions.Add("建议先创建全书大纲，为后续创作提供指导");
            }

            if (result.ChapterOutlineCount < result.ChapterCount)
            {
                result.Suggestions.Add("部分章节缺少细纲，建议补充章节细纲");
            }

            if (result.MissingSettings.Count > 0)
            {
                result.Suggestions.Add($"缺少{result.MissingSettings.Count}个设定文件，建议补充世界设定");
            }

            var incompleteChapters = result.ChapterDetails.Count(c => !c.IsComplete);
            if (incompleteChapters > 0)
            {
                result.Suggestions.Add($"有{incompleteChapters}章内容不完整，建议使用章节修复功能");
            }

            var problematicChapters = result.ChapterDetails.Count(c => c.HasIssues);
            if (problematicChapters > 0)
            {
                result.Suggestions.Add($"有{problematicChapters}章存在格式问题，建议进行内容清理");
            }
        }
    }

    /// <summary>
    /// 项目分析结果
    /// </summary>
    public class ProjectAnalysisResult
    {
        public string ProjectPath { get; set; } = "";
        public DateTime AnalysisTime { get; set; }
        
        // 文件夹结构
        public List<string> ExistingFolders { get; set; } = new();
        public List<string> MissingFolders { get; set; } = new();
        public Dictionary<string, int> FolderFileCounts { get; set; } = new();
        
        // 章节分析
        public int ChapterCount { get; set; }
        public List<ChapterInfo> ChapterDetails { get; set; } = new();
        
        // 大纲分析
        public bool HasOverallOutline { get; set; }
        public int ChapterOutlineCount { get; set; }
        public int VolumeOutlineCount { get; set; }
        
        // 设定分析
        public List<string> ExistingSettings { get; set; } = new();
        public List<string> MissingSettings { get; set; } = new();
        
        // 问题和建议
        public List<string> Issues { get; set; } = new();
        public List<string> Suggestions { get; set; } = new();
    }

    /// <summary>
    /// 章节信息
    /// </summary>
    public class ChapterInfo
    {
        public string FileName { get; set; } = "";
        public string FilePath { get; set; } = "";
        public int WordCount { get; set; }
        public bool IsComplete { get; set; }
        public bool HasIssues { get; set; }
    }
}

/// <summary>
/// 使用说明：
/// 
/// 1. 直接运行分析：
///    var analyzer = new ProjectAnalyzer();
///    var result = await analyzer.AnalyzeProjectAsync();
/// 
/// 2. 指定项目路径：
///    var analyzer = new ProjectAnalyzer(@"D:\你的项目路径");
///    var result = await analyzer.AnalyzeProjectAsync();
/// 
/// 3. 查看分析结果：
///    Console.WriteLine($"章节数量: {result.ChapterCount}");
///    Console.WriteLine($"建议: {string.Join(", ", result.Suggestions)}");
/// </summary>
