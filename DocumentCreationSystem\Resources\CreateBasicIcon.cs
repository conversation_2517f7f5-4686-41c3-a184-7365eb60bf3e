using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;

namespace DocumentCreationSystem.Resources
{
    /// <summary>
    /// 基本图标创建器
    /// 用于在没有外部工具时创建简单的应用程序图标
    /// </summary>
    public static class CreateBasicIcon
    {
        /// <summary>
        /// 创建基本的应用程序图标
        /// </summary>
        public static void GenerateBasicAppIcon()
        {
            try
            {
                // 创建输出目录
                var outputDir = "Generated";
                if (!Directory.Exists(outputDir))
                {
                    Directory.CreateDirectory(outputDir);
                }

                // 生成不同尺寸的图标
                var sizes = new[] { 16, 24, 32, 48, 64, 128, 256 };
                
                foreach (var size in sizes)
                {
                    var bitmap = CreateIconBitmap(size);
                    var filename = Path.Combine(outputDir, $"AppIcon_{size}.png");
                    bitmap.Save(filename, ImageFormat.Png);
                    bitmap.Dispose();
                    Console.WriteLine($"生成图标: {filename}");
                }

                // 创建ICO文件
                CreateIcoFile(outputDir);
                
                Console.WriteLine("基本图标生成完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"生成图标时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建指定尺寸的图标位图
        /// </summary>
        private static Bitmap CreateIconBitmap(int size)
        {
            var bitmap = new Bitmap(size, size, PixelFormat.Format32bppArgb);
            
            using (var g = Graphics.FromImage(bitmap))
            {
                g.SmoothingMode = SmoothingMode.AntiAlias;
                g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                g.PixelOffsetMode = PixelOffsetMode.HighQuality;

                // 清除背景
                g.Clear(Color.Transparent);

                // 绘制背景圆形
                var bgBrush = new LinearGradientBrush(
                    new Point(0, 0), 
                    new Point(size, size),
                    Color.FromArgb(74, 144, 226),   // #4A90E2
                    Color.FromArgb(53, 122, 189)    // #357ABD
                );
                
                var bgRect = new Rectangle(2, 2, size - 4, size - 4);
                g.FillEllipse(bgBrush, bgRect);

                // 绘制文档图标
                var docWidth = size * 0.4f;
                var docHeight = size * 0.5f;
                var docX = size * 0.2f;
                var docY = size * 0.15f;
                
                var docRect = new RectangleF(docX, docY, docWidth, docHeight);
                g.FillRectangle(Brushes.White, docRect);

                // 绘制文档折角
                var cornerSize = docWidth * 0.2f;
                var cornerPoints = new PointF[]
                {
                    new PointF(docX + docWidth - cornerSize, docY),
                    new PointF(docX + docWidth, docY + cornerSize),
                    new PointF(docX + docWidth - cornerSize, docY + cornerSize)
                };
                g.FillPolygon(Brushes.LightGray, cornerPoints);

                // 绘制文档内容线条（只在较大尺寸时绘制）
                if (size >= 32)
                {
                    var lineHeight = Math.Max(1, size / 32);
                    var lineSpacing = Math.Max(2, size / 16);
                    var lineX = docX + docWidth * 0.1f;
                    var lineWidth = docWidth * 0.8f;
                    
                    for (int i = 0; i < 4; i++)
                    {
                        var lineY = docY + docHeight * 0.3f + i * lineSpacing;
                        var currentLineWidth = (i % 2 == 0) ? lineWidth : lineWidth * 0.8f;
                        g.FillRectangle(Brushes.LightGray, lineX, lineY, currentLineWidth, lineHeight);
                    }
                }

                // 绘制AI标识
                var aiSize = size * 0.25f;
                var aiX = size * 0.65f;
                var aiY = size * 0.65f;
                
                var aiBrush = new LinearGradientBrush(
                    new PointF(aiX, aiY),
                    new PointF(aiX + aiSize, aiY + aiSize),
                    Color.FromArgb(123, 104, 238),  // #7B68EE
                    Color.FromArgb(106, 90, 205)    // #6A5ACD
                );
                
                g.FillEllipse(aiBrush, aiX, aiY, aiSize, aiSize);

                // 绘制AI文字（只在较大尺寸时绘制）
                if (size >= 24)
                {
                    var fontSize = Math.Max(6, size / 8);
                    var font = new Font("Arial", fontSize, FontStyle.Bold);
                    var textSize = g.MeasureString("AI", font);
                    var textX = aiX + (aiSize - textSize.Width) / 2;
                    var textY = aiY + (aiSize - textSize.Height) / 2;
                    
                    g.DrawString("AI", font, Brushes.White, textX, textY);
                    font.Dispose();
                }

                // 绘制智能笔（只在较大尺寸时绘制）
                if (size >= 48)
                {
                    var penWidth = size * 0.08f;
                    var penHeight = size * 0.3f;
                    var penX = size * 0.75f;
                    var penY = size * 0.25f;
                    
                    // 旋转画笔
                    g.TranslateTransform(penX, penY);
                    g.RotateTransform(-30);
                    
                    var penBrush = new LinearGradientBrush(
                        new PointF(0, 0),
                        new PointF(penWidth, penHeight),
                        Color.FromArgb(255, 107, 107), // #FF6B6B
                        Color.FromArgb(229, 85, 85)    // #E55555
                    );
                    
                    g.FillRectangle(penBrush, 0, 0, penWidth, penHeight);
                    
                    // 笔尖
                    var tipPoints = new PointF[]
                    {
                        new PointF(penWidth / 2, penHeight),
                        new PointF(0, penHeight + penWidth),
                        new PointF(penWidth, penHeight + penWidth)
                    };
                    g.FillPolygon(Brushes.DarkGray, tipPoints);
                    
                    // 笔帽
                    g.FillRectangle(Brushes.Gold, 0, -penWidth, penWidth, penWidth);
                    
                    g.ResetTransform();
                    
                    penBrush.Dispose();
                }

                bgBrush.Dispose();
                aiBrush.Dispose();
            }

            return bitmap;
        }

        /// <summary>
        /// 创建ICO文件
        /// </summary>
        private static void CreateIcoFile(string outputDir)
        {
            try
            {
                // 这里只创建一个简单的32x32 ICO文件
                var bitmap32 = CreateIconBitmap(32);
                var icoPath = Path.Combine(outputDir, "AppIcon.ico");
                
                // 简单的ICO文件创建（只包含32x32）
                using (var fs = new FileStream(icoPath, FileMode.Create))
                {
                    // ICO文件头
                    fs.Write(new byte[] { 0, 0 }, 0, 2); // Reserved
                    fs.Write(new byte[] { 1, 0 }, 0, 2); // Type (1 = ICO)
                    fs.Write(new byte[] { 1, 0 }, 0, 2); // Count (1 image)
                    
                    // 图像目录条目
                    fs.WriteByte(32); // Width
                    fs.WriteByte(32); // Height
                    fs.WriteByte(0);  // Color count
                    fs.WriteByte(0);  // Reserved
                    fs.Write(new byte[] { 1, 0 }, 0, 2); // Planes
                    fs.Write(new byte[] { 32, 0 }, 0, 2); // Bits per pixel
                    
                    // 将位图转换为PNG数据
                    using (var ms = new MemoryStream())
                    {
                        bitmap32.Save(ms, ImageFormat.Png);
                        var pngData = ms.ToArray();
                        
                        // 数据大小
                        var size = BitConverter.GetBytes(pngData.Length);
                        fs.Write(size, 0, 4);
                        
                        // 数据偏移
                        var offset = BitConverter.GetBytes(22); // 6 + 16 = 22
                        fs.Write(offset, 0, 4);
                        
                        // PNG数据
                        fs.Write(pngData, 0, pngData.Length);
                    }
                }
                
                bitmap32.Dispose();
                Console.WriteLine($"生成ICO文件: {icoPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建ICO文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 主入口点（用于独立运行）
        /// </summary>
        public static void Main(string[] args)
        {
            Console.WriteLine("文档创建管理系统 - 基本图标生成器");
            Console.WriteLine("=====================================");
            GenerateBasicAppIcon();
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
