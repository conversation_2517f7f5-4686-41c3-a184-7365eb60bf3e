using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using DocumentCreationSystem.Services;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 测试项目清理功能
    /// </summary>
    public class TestProjectCleanup
    {
        public static async Task Main(string[] args)
        {
            // 设置依赖注入
            var services = new ServiceCollection();
            services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
            services.AddScoped<ProjectCleanupService>();
            services.AddScoped<ContentQualityService>();

            var serviceProvider = services.BuildServiceProvider();
            var logger = serviceProvider.GetRequiredService<ILogger<TestProjectCleanup>>();
            var cleanupService = serviceProvider.GetRequiredService<ProjectCleanupService>();

            try
            {
                Console.WriteLine("=== 项目文件夹清理测试工具 ===");
                Console.WriteLine();

                // 获取项目路径
                string projectPath;
                if (args.Length > 0)
                {
                    projectPath = args[0];
                }
                else
                {
                    Console.Write("请输入项目路径: ");
                    projectPath = Console.ReadLine()?.Trim() ?? "";
                }

                if (string.IsNullOrEmpty(projectPath) || !Directory.Exists(projectPath))
                {
                    Console.WriteLine("错误: 项目路径无效或不存在");
                    return;
                }

                Console.WriteLine($"项目路径: {projectPath}");
                Console.WriteLine();

                // 显示清理前的文件夹结构
                Console.WriteLine("=== 清理前的文件夹结构 ===");
                DisplayFolderStructure(projectPath);
                Console.WriteLine();

                // 执行清理
                Console.WriteLine("=== 开始清理空文件夹 ===");
                var result = await cleanupService.CleanupEmptyFoldersAsync(projectPath);

                // 显示清理结果
                Console.WriteLine("=== 清理结果 ===");
                Console.WriteLine($"清理状态: {(result.IsSuccess ? "成功" : "失败")}");
                Console.WriteLine($"消息: {result.Message}");

                if (!string.IsNullOrEmpty(result.ErrorMessage))
                {
                    Console.WriteLine($"错误: {result.ErrorMessage}");
                }

                if (result.DeletedFolders.Count > 0)
                {
                    Console.WriteLine();
                    Console.WriteLine("已删除的空文件夹:");
                    foreach (var folder in result.DeletedFolders)
                    {
                        Console.WriteLine($"  - {folder}");
                    }
                }

                if (result.NonEmptyFolders.Count > 0)
                {
                    Console.WriteLine();
                    Console.WriteLine("保留的非空文件夹:");
                    foreach (var folder in result.NonEmptyFolders)
                    {
                        Console.WriteLine($"  - {folder}");
                    }
                }

                if (result.Errors.Count > 0)
                {
                    Console.WriteLine();
                    Console.WriteLine("错误信息:");
                    foreach (var error in result.Errors)
                    {
                        Console.WriteLine($"  - {error}");
                    }
                }

                Console.WriteLine();

                // 显示清理后的文件夹结构
                Console.WriteLine("=== 清理后的文件夹结构 ===");
                DisplayFolderStructure(projectPath);
                Console.WriteLine();

                Console.WriteLine("清理完成！按任意键退出...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "测试过程中发生错误");
                Console.WriteLine($"错误: {ex.Message}");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
        }

        /// <summary>
        /// 显示文件夹结构
        /// </summary>
        private static void DisplayFolderStructure(string projectPath, string indent = "", int maxDepth = 2, int currentDepth = 0)
        {
            try
            {
                if (currentDepth >= maxDepth)
                    return;

                var directories = Directory.GetDirectories(projectPath);
                foreach (var dir in directories)
                {
                    var dirName = Path.GetFileName(dir);
                    var fileCount = Directory.GetFiles(dir, "*", SearchOption.AllDirectories).Length;
                    var subDirCount = Directory.GetDirectories(dir, "*", SearchOption.AllDirectories).Length;
                    
                    Console.WriteLine($"{indent}📁 {dirName} ({fileCount} 文件, {subDirCount} 子文件夹)");
                    
                    // 递归显示子文件夹
                    if (currentDepth < maxDepth - 1)
                    {
                        DisplayFolderStructure(dir, indent + "  ", maxDepth, currentDepth + 1);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{indent}❌ 无法访问文件夹: {ex.Message}");
            }
        }
    }
}
