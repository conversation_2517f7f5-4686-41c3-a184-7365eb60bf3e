<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="documentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357ABD;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="aiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7B68EE;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6A5ACD;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="penGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E55555;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    
    <!-- 发光效果 -->
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="128" cy="128" r="120" fill="url(#documentGradient)" filter="url(#shadow)"/>
  
  <!-- 主文档图标 -->
  <g transform="translate(64, 48)">
    <!-- 文档背景 -->
    <rect x="0" y="0" width="96" height="128" rx="8" ry="8" fill="#FFFFFF" filter="url(#shadow)"/>
    
    <!-- 文档折角 -->
    <path d="M 80 0 L 96 16 L 80 16 Z" fill="#E8E8E8"/>
    
    <!-- 文档内容线条 -->
    <rect x="12" y="24" width="60" height="3" rx="1.5" fill="#CCCCCC"/>
    <rect x="12" y="32" width="72" height="3" rx="1.5" fill="#CCCCCC"/>
    <rect x="12" y="40" width="56" height="3" rx="1.5" fill="#CCCCCC"/>
    <rect x="12" y="48" width="68" height="3" rx="1.5" fill="#CCCCCC"/>
    
    <!-- AI智能标识 -->
    <circle cx="72" cy="96" r="16" fill="url(#aiGradient)" filter="url(#glow)"/>
    <text x="72" y="102" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF">AI</text>
  </g>
  
  <!-- 智能笔图标 -->
  <g transform="translate(160, 140) rotate(-30)">
    <!-- 笔身 -->
    <rect x="0" y="0" width="8" height="48" rx="4" fill="url(#penGradient)" filter="url(#shadow)"/>
    
    <!-- 笔尖 -->
    <polygon points="4,48 0,56 8,56" fill="#333333"/>
    
    <!-- 笔帽 -->
    <rect x="0" y="-8" width="8" height="8" rx="4" fill="#FFD700"/>
    
    <!-- 智能光点 -->
    <circle cx="4" cy="12" r="1.5" fill="#FFFFFF" opacity="0.8"/>
    <circle cx="4" cy="20" r="1" fill="#FFFFFF" opacity="0.6"/>
    <circle cx="4" cy="28" r="1.5" fill="#FFFFFF" opacity="0.8"/>
  </g>
  
  <!-- 创作灵感线条 -->
  <g opacity="0.6">
    <path d="M 180 120 Q 200 100 220 120" stroke="#FFD700" stroke-width="2" fill="none" stroke-linecap="round"/>
    <path d="M 185 130 Q 205 110 225 130" stroke="#FFD700" stroke-width="1.5" fill="none" stroke-linecap="round"/>
    <path d="M 190 140 Q 210 120 230 140" stroke="#FFD700" stroke-width="1" fill="none" stroke-linecap="round"/>
  </g>
  
  <!-- 管理齿轮图标 -->
  <g transform="translate(48, 180)">
    <circle cx="16" cy="16" r="12" fill="none" stroke="#FFFFFF" stroke-width="2" opacity="0.8"/>
    <circle cx="16" cy="16" r="6" fill="#FFFFFF" opacity="0.8"/>
    
    <!-- 齿轮齿 -->
    <g stroke="#FFFFFF" stroke-width="2" opacity="0.8">
      <line x1="16" y1="0" x2="16" y2="4"/>
      <line x1="16" y1="28" x2="16" y2="32"/>
      <line x1="0" y1="16" x2="4" y2="16"/>
      <line x1="28" y1="16" x2="32" y2="16"/>
      <line x1="6.34" y1="6.34" x2="9.17" y2="9.17"/>
      <line x1="22.83" y1="22.83" x2="25.66" y2="25.66"/>
      <line x1="25.66" y1="6.34" x2="22.83" y2="9.17"/>
      <line x1="9.17" y1="22.83" x2="6.34" y2="25.66"/>
    </g>
  </g>
  
  <!-- 项目文件夹图标 -->
  <g transform="translate(180, 60)">
    <path d="M 0 8 L 0 24 L 32 24 L 32 4 L 16 4 L 12 0 L 0 0 Z" fill="#FFA500" opacity="0.8" filter="url(#shadow)"/>
    <rect x="2" y="6" width="28" height="16" fill="#FFB84D" opacity="0.9"/>
    
    <!-- 文件图标 -->
    <rect x="6" y="10" width="8" height="10" rx="1" fill="#FFFFFF" opacity="0.9"/>
    <rect x="16" y="12" width="8" height="8" rx="1" fill="#FFFFFF" opacity="0.9"/>
  </g>
  
  <!-- 版本标识 -->
  <text x="128" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#FFFFFF" opacity="0.7">DCS</text>
</svg>
