#!/usr/bin/env python3
"""
简单图标生成器
为文档创建管理系统生成基本的ICO文件
"""

import struct
import os

def create_simple_ico():
    """创建一个简单的32x32 ICO文件"""
    
    # 创建32x32的位图数据 (简化版本)
    width, height = 32, 32
    
    # ICO文件头 (6字节)
    ico_header = struct.pack('<HHH', 0, 1, 1)  # Reserved, Type, Count
    
    # 图像目录条目 (16字节)
    ico_entry = struct.pack('<BBBBHHLL', 
                           width,      # Width
                           height,     # Height  
                           0,          # Color count
                           0,          # Reserved
                           1,          # Planes
                           32,         # Bits per pixel
                           0,          # Image size (will be updated)
                           22)         # Image offset
    
    # 创建简单的位图数据
    # 这里创建一个蓝色背景的简单图标
    bitmap_data = bytearray()
    
    # 位图信息头 (40字节)
    bitmap_info = struct.pack('<LLLHHLLLLLL',
                             40,         # Header size
                             width,      # Width
                             height * 2, # Height (doubled for ICO)
                             1,          # Planes
                             32,         # Bits per pixel
                             0,          # Compression
                             0,          # Image size
                             0,          # X pixels per meter
                             0,          # Y pixels per meter
                             0,          # Colors used
                             0)          # Important colors
    
    bitmap_data.extend(bitmap_info)
    
    # 像素数据 (32x32 BGRA)
    for y in range(height):
        for x in range(width):
            # 创建一个简单的渐变效果
            center_x, center_y = width // 2, height // 2
            distance = ((x - center_x) ** 2 + (y - center_y) ** 2) ** 0.5
            max_distance = (width // 2)
            
            if distance <= max_distance:
                # 蓝色渐变
                intensity = 1.0 - (distance / max_distance)
                blue = int(226 * intensity)   # #4A90E2的蓝色分量
                green = int(144 * intensity)  # #4A90E2的绿色分量
                red = int(74 * intensity)     # #4A90E2的红色分量
                alpha = 255
                
                # 在中心添加白色文档图标
                if (8 <= x <= 24 and 6 <= y <= 26):
                    if (x == 8 or x == 24 or y == 6 or y == 26):
                        # 文档边框
                        blue, green, red = 255, 255, 255
                    elif (10 <= x <= 22 and 8 <= y <= 24):
                        # 文档内容
                        blue, green, red = 255, 255, 255
                        
                # 添加AI标识
                if (20 <= x <= 28 and 20 <= y <= 28):
                    ai_distance = ((x - 24) ** 2 + (y - 24) ** 2) ** 0.5
                    if ai_distance <= 4:
                        blue, green, red = 205, 90, 106  # 紫色
                        
            else:
                blue = green = red = alpha = 0
            
            # BGRA格式
            bitmap_data.extend([blue, green, red, alpha])
    
    # AND掩码 (1位每像素，32字节)
    and_mask = bytearray(width * height // 8)
    bitmap_data.extend(and_mask)
    
    # 更新图像大小
    image_size = len(bitmap_data)
    ico_entry = ico_entry[:8] + struct.pack('<L', image_size) + ico_entry[12:]
    
    # 写入ICO文件
    output_dir = "Generated"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    ico_path = os.path.join(output_dir, "AppIcon.ico")
    
    with open(ico_path, 'wb') as f:
        f.write(ico_header)
        f.write(ico_entry)
        f.write(bitmap_data)
    
    print(f"✓ 生成ICO文件: {ico_path}")
    print(f"文件大小: {os.path.getsize(ico_path)} 字节")
    
    return ico_path

if __name__ == "__main__":
    print("文档创建管理系统 - 简单图标生成器")
    print("=====================================")
    
    try:
        ico_path = create_simple_ico()
        print("\n图标生成完成！")
        print(f"请将 {ico_path} 复制到项目的 Resources 目录")
        print("然后重新编译项目即可看到新图标")
        
    except Exception as e:
        print(f"生成图标时发生错误: {e}")
        
    input("\n按回车键退出...")
