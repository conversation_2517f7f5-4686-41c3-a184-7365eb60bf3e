using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using DocumentCreationSystem.Services;
using DocumentCreationSystem.Models;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 测试增强文件访问功能
    /// </summary>
    public class TestEnhancedFileAccess
    {
        private readonly EnhancedFileAccessService _fileService;
        private readonly ToolCallCompatibilityService _compatibilityService;
        private readonly ILogger<TestEnhancedFileAccess> _logger;

        public TestEnhancedFileAccess()
        {
            // 设置依赖注入
            var services = new ServiceCollection();
            services.AddLogging(builder => builder.AddConsole());
            services.AddSingleton<ProjectService>();
            services.AddSingleton<AgentToolService>();
            services.AddSingleton<EnhancedFileAccessService>();
            services.AddSingleton<ToolCallCompatibilityService>();

            var serviceProvider = services.BuildServiceProvider();
            _fileService = serviceProvider.GetRequiredService<EnhancedFileAccessService>();
            _compatibilityService = serviceProvider.GetRequiredService<ToolCallCompatibilityService>();
            _logger = serviceProvider.GetRequiredService<ILogger<TestEnhancedFileAccess>>();
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public async Task RunAllTestsAsync()
        {
            Console.WriteLine("🧪 开始测试增强文件访问功能...");
            Console.WriteLine(new string('=', 60));

            await TestPathInfo();
            await TestSmartFileAccess();
            await TestPathSearch();
            await TestBatchFileInfo();
            await TestDirectoryAnalysis();
            await TestToolCompatibility();

            Console.WriteLine(new string('=', 60));
            Console.WriteLine("✅ 所有测试完成！");
        }

        /// <summary>
        /// 测试路径信息获取
        /// </summary>
        private async Task TestPathInfo()
        {
            Console.WriteLine("📍 测试路径信息获取...");

            try
            {
                var currentDir = Directory.GetCurrentDirectory();
                var result = await _fileService.GetPathInfoAsync(currentDir, false, 2);
                
                Console.WriteLine($"  📁 路径: {result.FullPath}");
                Console.WriteLine($"  📊 类型: {(result.IsDirectory ? "目录" : "文件")}");
                Console.WriteLine($"  📄 文件数: {result.FileCount}");
                Console.WriteLine($"  📁 目录数: {result.DirectoryCount}");
                Console.WriteLine($"  💾 总大小: {FormatFileSize(result.TotalSize)}");
                
                if (result.FileTypeDistribution?.Count > 0)
                {
                    Console.WriteLine("  📊 文件类型分布:");
                    foreach (var type in result.FileTypeDistribution)
                    {
                        Console.WriteLine($"    • {type.Key}: {type.Value} 个");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 测试失败: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试智能文件访问
        /// </summary>
        private async Task TestSmartFileAccess()
        {
            Console.WriteLine("🧠 测试智能文件访问...");

            try
            {
                // 查找一些测试文件
                var testFiles = Directory.GetFiles(".", "*.cs", SearchOption.TopDirectoryOnly);
                
                if (testFiles.Length > 0)
                {
                    var testFile = testFiles[0];
                    Console.WriteLine($"  📄 测试文件: {Path.GetFileName(testFile)}");
                    
                    var result = await _fileService.GetFileContentAsync(testFile, FileAccessMode.Smart);
                    
                    Console.WriteLine($"  📊 访问策略: {result.AccessStrategy}");
                    Console.WriteLine($"  📏 文件大小: {FormatFileSize(result.FileSize)}");
                    Console.WriteLine($"  ✅ 完整读取: {result.IsComplete}");
                    
                    if (result.TotalLines.HasValue)
                    {
                        Console.WriteLine($"  📝 总行数: {result.TotalLines}");
                    }
                    
                    if (result.PreviewLines.HasValue)
                    {
                        Console.WriteLine($"  👀 预览行数: {result.PreviewLines}");
                    }
                    
                    if (!string.IsNullOrEmpty(result.Content))
                    {
                        var preview = result.Content.Length > 200 ? result.Content.Substring(0, 200) + "..." : result.Content;
                        Console.WriteLine($"  📄 内容预览: {preview}");
                    }
                }
                else
                {
                    Console.WriteLine("  ⚠️ 未找到测试文件");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 测试失败: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试路径搜索
        /// </summary>
        private async Task TestPathSearch()
        {
            Console.WriteLine("🔍 测试路径搜索...");

            try
            {
                var searchOptions = new PathSearchOptions
                {
                    Recursive = false,
                    IncludeDetailedInfo = true,
                    MaxResults = 10
                };

                var result = await _fileService.SearchPathsAsync(".", "*.cs", searchOptions);
                
                Console.WriteLine($"  📊 总匹配数: {result.TotalMatches}");
                Console.WriteLine($"  📄 匹配文件: {result.MatchedFiles.Count}");
                Console.WriteLine($"  📁 匹配目录: {result.MatchedDirectories.Count}");
                
                if (result.MatchedFiles.Count > 0)
                {
                    Console.WriteLine("  📋 匹配的文件:");
                    foreach (var file in result.MatchedFiles.Take(5))
                    {
                        Console.WriteLine($"    📄 {Path.GetFileName(file)}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 测试失败: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试批量文件信息
        /// </summary>
        private async Task TestBatchFileInfo()
        {
            Console.WriteLine("📊 测试批量文件信息...");

            try
            {
                var testFiles = Directory.GetFiles(".", "*.cs", SearchOption.TopDirectoryOnly).Take(3);
                
                if (testFiles.Any())
                {
                    var result = await _fileService.GetBatchFileInfoAsync(testFiles, false);
                    
                    Console.WriteLine($"  📄 总文件数: {result.TotalFiles}");
                    Console.WriteLine($"  ✅ 有效文件: {result.ValidFiles}");
                    Console.WriteLine($"  📝 文本文件: {result.TextFiles}");
                    Console.WriteLine($"  💾 总大小: {FormatFileSize(result.TotalSize)}");
                    
                    Console.WriteLine("  📋 文件列表:");
                    foreach (var file in result.Files)
                    {
                        var status = file.Exists ? "✅" : "❌";
                        Console.WriteLine($"    {status} {file.FileName} ({FormatFileSize(file.FileSize)})");
                    }
                }
                else
                {
                    Console.WriteLine("  ⚠️ 未找到测试文件");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 测试失败: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试目录分析
        /// </summary>
        private async Task TestDirectoryAnalysis()
        {
            Console.WriteLine("🏗️ 测试目录分析...");

            try
            {
                var currentDir = Directory.GetCurrentDirectory();
                var result = await _fileService.GetPathInfoAsync(currentDir, false, 1);
                
                Console.WriteLine($"  📍 分析目录: {result.Name}");
                Console.WriteLine($"  📁 子目录数: {result.DirectoryCount}");
                Console.WriteLine($"  📄 文件数: {result.FileCount}");
                Console.WriteLine($"  💾 总大小: {FormatFileSize(result.TotalSize)}");
                
                if (result.Children?.Any() == true)
                {
                    Console.WriteLine("  🌳 主要内容:");
                    foreach (var child in result.Children.Take(10))
                    {
                        var icon = child.IsDirectory ? "📁" : "📄";
                        var sizeInfo = child.IsFile ? $" ({FormatFileSize(child.FileSize)})" : "";
                        Console.WriteLine($"    {icon} {child.Name}{sizeInfo}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 测试失败: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试工具兼容性
        /// </summary>
        private async Task TestToolCompatibility()
        {
            Console.WriteLine("🔧 测试工具兼容性...");

            try
            {
                // 测试工具调用检测
                var testCalls = new[]
                {
                    "get_path_info(.)",
                    "smart_file_access(test.cs)",
                    "search_paths(.|*.cs)",
                    "analyze_directory_structure(.)"
                };

                foreach (var call in testCalls)
                {
                    var hasToolCall = _compatibilityService.ContainsToolCalls(call);
                    Console.WriteLine($"  📝 '{call}' -> {(hasToolCall ? "✅ 检测到工具调用" : "❌ 未检测到")}");
                }

                // 测试工具指南生成
                var guide = _compatibilityService.GenerateToolGuide();
                Console.WriteLine($"  📖 工具指南长度: {guide.Length} 字符");
                
                // 显示支持的工具数量
                var tools = _compatibilityService.GetSupportedTools();
                Console.WriteLine($"  🛠️ 支持的工具数量: {tools.Count}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ 测试失败: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            if (bytes < 1024) return $"{bytes} B";
            if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} KB";
            if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024 * 1024):F1} MB";
            return $"{bytes / (1024 * 1024 * 1024):F1} GB";
        }

        /// <summary>
        /// 主入口点
        /// </summary>
        public static async Task Main(string[] args)
        {
            try
            {
                var tester = new TestEnhancedFileAccess();
                await tester.RunAllTestsAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}

/// <summary>
/// 使用说明：
/// 
/// 1. 运行测试：
///    dotnet run --project DocumentCreationSystem TestEnhancedFileAccess.cs
/// 
/// 2. 功能说明：
///    - 测试路径信息获取功能
///    - 测试智能文件访问策略
///    - 测试路径搜索功能
///    - 测试批量文件信息获取
///    - 测试目录结构分析
///    - 测试工具调用兼容性
/// 
/// 3. 验证的增强功能：
///    - 智能文件访问策略选择
///    - 根据文件大小自动选择读取方式
///    - 复杂路径搜索和过滤
///    - 批量文件操作
///    - 深度目录分析
///    - 工具调用文本解析
/// </summary>
