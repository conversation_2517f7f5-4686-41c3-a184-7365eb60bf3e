using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using DocumentCreationSystem.Services;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 测试工具调用兼容性服务
    /// </summary>
    public class TestToolCompatibility
    {
        private readonly ToolCallCompatibilityService _compatibilityService;
        private readonly ILogger<TestToolCompatibility> _logger;

        public TestToolCompatibility()
        {
            // 设置依赖注入
            var services = new ServiceCollection();
            services.AddLogging(builder => builder.AddConsole());
            services.AddSingleton<ProjectService>();
            services.AddSingleton<AgentToolService>();
            services.AddSingleton<ToolCallCompatibilityService>();

            var serviceProvider = services.BuildServiceProvider();
            _compatibilityService = serviceProvider.GetRequiredService<ToolCallCompatibilityService>();
            _logger = serviceProvider.GetRequiredService<ILogger<TestToolCompatibility>>();
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public async Task RunAllTestsAsync()
        {
            Console.WriteLine("🧪 开始测试工具调用兼容性服务...");
            Console.WriteLine(new string('=', 60));

            await TestToolDetection();
            await TestProjectAnalysis();
            await TestToolCallProcessing();
            await TestGuiYiProjectAnalysis();
            await ShowToolGuide();

            Console.WriteLine(new string('=', 60));
            Console.WriteLine("✅ 所有测试完成！");
        }

        /// <summary>
        /// 测试工具检测功能
        /// </summary>
        private async Task TestToolDetection()
        {
            Console.WriteLine("🔍 测试工具检测功能...");

            var testTexts = new[]
            {
                "请帮我分析项目结构: scan_project_structure()",
                "查看项目概览: get_project_overview()",
                "搜索关键词'主角': deep_content_search(主角)",
                "这是普通文本，没有工具调用",
                "分析文件: get_file_summary(第一章.txt) 和 analyze_file_relationships()"
            };

            foreach (var text in testTexts)
            {
                var hasToolCalls = _compatibilityService.ContainsToolCalls(text);
                Console.WriteLine($"  📝 '{text}' -> {(hasToolCalls ? "✅ 包含工具调用" : "❌ 无工具调用")}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试项目分析功能
        /// </summary>
        private async Task TestProjectAnalysis()
        {
            Console.WriteLine("📊 测试项目分析功能...");

            try
            {
                var result = await _compatibilityService.AnalyzeGuiYiProject();
                Console.WriteLine("项目分析结果:");
                Console.WriteLine(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 项目分析失败: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试工具调用处理
        /// </summary>
        private async Task TestToolCallProcessing()
        {
            Console.WriteLine("⚙️ 测试工具调用处理...");

            var testText = @"
用户请求分析项目，我需要执行以下操作：

1. 首先扫描项目结构: scan_project_structure()
2. 然后获取项目概览: get_project_overview()
3. 最后分析文件关系: analyze_file_relationships()

这样就能全面了解项目情况了。
";

            try
            {
                var result = await _compatibilityService.ProcessToolCallsAsync(testText);
                Console.WriteLine("处理结果:");
                Console.WriteLine(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 工具调用处理失败: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试诡异收藏家项目分析
        /// </summary>
        private async Task TestGuiYiProjectAnalysis()
        {
            Console.WriteLine("📚 测试诡异收藏家项目专项分析...");

            try
            {
                // 模拟用户请求分析诡异收藏家项目
                var userRequest = @"
请帮我分析'诡异收藏家'项目的内容及结构。我需要了解：
1. 项目的整体结构: scan_project_structure()
2. 项目的基本信息: get_project_overview()
3. 如果有内容文件，请搜索主要角色: deep_content_search(主角)
";

                var result = await _compatibilityService.ProcessToolCallsAsync(userRequest);
                Console.WriteLine("分析结果:");
                Console.WriteLine(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 诡异收藏家项目分析失败: {ex.Message}");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 显示工具使用指南
        /// </summary>
        private async Task ShowToolGuide()
        {
            Console.WriteLine("📖 工具使用指南:");
            var guide = _compatibilityService.GenerateToolGuide();
            Console.WriteLine(guide);
        }

        /// <summary>
        /// 主入口点
        /// </summary>
        public static async Task Main(string[] args)
        {
            try
            {
                var tester = new TestToolCompatibility();
                await tester.RunAllTestsAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}

/// <summary>
/// 使用说明：
/// 
/// 1. 直接运行测试：
///    dotnet run --project DocumentCreationSystem TestToolCompatibility.cs
/// 
/// 2. 功能说明：
///    - 测试工具调用检测功能
///    - 测试项目分析功能
///    - 测试工具调用处理流程
///    - 专门测试诡异收藏家项目分析
///    - 显示工具使用指南
/// 
/// 3. 解决的问题：
///    - 原生不支持函数调用的AI模型无法使用工具
///    - 通过文本解析方式实现工具调用兼容性
///    - 支持多种工具调用语法格式
///    - 提供详细的错误处理和日志记录
/// 
/// 4. 支持的工具调用格式：
///    - scan_project_structure()
///    - get_project_overview()
///    - deep_content_search(关键词)
///    - get_file_summary(文件路径)
///    - analyze_file_relationships()
///    - extract_key_information(文件路径|类型)
///    - get_content_statistics(路径)
/// 
/// 5. 使用场景：
///    - AI助手对话中自动识别和执行工具调用
///    - 项目分析和内容检索
///    - 文件关系分析和统计
///    - 支持不同AI模型的工具调用需求
/// </summary>
