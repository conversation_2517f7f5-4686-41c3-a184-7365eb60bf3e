using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using DocumentCreationSystem.Services;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 测试文件夹结构修复效果
    /// </summary>
    public class TestFolderStructureFix
    {
        public static async Task Main(string[] args)
        {
            // 设置依赖注入
            var services = new ServiceCollection();
            services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
            services.AddScoped<IProjectService, ProjectService>();
            services.AddScoped<IDataStorage, SqliteDataStorage>();
            services.AddScoped<ProjectCleanupService>();
            services.AddScoped<ContentQualityService>();

            var serviceProvider = services.BuildServiceProvider();
            var logger = serviceProvider.GetRequiredService<ILogger<TestFolderStructureFix>>();
            var projectService = serviceProvider.GetRequiredService<IProjectService>();
            var cleanupService = serviceProvider.GetRequiredService<ProjectCleanupService>();

            try
            {
                Console.WriteLine("=== 文件夹结构修复测试 ===");
                Console.WriteLine();

                // 创建测试项目
                var testProjectPath = Path.Combine(Path.GetTempPath(), "TestProject_" + DateTime.Now.ToString("yyyyMMdd_HHmmss"));
                Console.WriteLine($"创建测试项目: {testProjectPath}");

                // 测试新的项目初始化
                Console.WriteLine("\n=== 测试新项目初始化 ===");
                var success = await projectService.InitializeProjectStructureAsync(testProjectPath, "Novel");
                Console.WriteLine($"项目初始化结果: {(success ? "成功" : "失败")}");

                // 显示创建的文件夹结构
                Console.WriteLine("\n=== 新项目文件夹结构 ===");
                DisplayFolderStructure(testProjectPath);

                // 验证只创建了必要的文件夹
                var expectedFolders = new[] { "文档", "资源", "备份", "导出", "章节", "大纲", "设定" };
                var unexpectedFolders = new[] { "世界设定", "角色设定", "时间线", "情节设定", "地图", "势力设定", "物品设定", "技能设定", "关系网络" };

                Console.WriteLine("\n=== 文件夹验证结果 ===");
                
                Console.WriteLine("应该存在的文件夹:");
                foreach (var folder in expectedFolders)
                {
                    var exists = Directory.Exists(Path.Combine(testProjectPath, folder));
                    Console.WriteLine($"  {folder}: {(exists ? "✓ 存在" : "✗ 不存在")}");
                }

                Console.WriteLine("\n不应该存在的文件夹:");
                foreach (var folder in unexpectedFolders)
                {
                    var exists = Directory.Exists(Path.Combine(testProjectPath, folder));
                    Console.WriteLine($"  {folder}: {(exists ? "✗ 意外存在" : "✓ 正确不存在")}");
                }

                // 测试清理功能（创建一些空文件夹来测试）
                Console.WriteLine("\n=== 测试清理功能 ===");
                
                // 创建一些旧的空文件夹来测试清理
                var oldFoldersToCreate = new[] { "世界设定", "角色设定", "时间线" };
                foreach (var folder in oldFoldersToCreate)
                {
                    Directory.CreateDirectory(Path.Combine(testProjectPath, folder));
                    Console.WriteLine($"创建测试空文件夹: {folder}");
                }

                // 创建一些大纲子文件夹
                var outlinePath = Path.Combine(testProjectPath, "大纲");
                Directory.CreateDirectory(Path.Combine(outlinePath, "分卷大纲"));
                Directory.CreateDirectory(Path.Combine(outlinePath, "章节细纲"));
                Console.WriteLine("创建测试大纲子文件夹");

                Console.WriteLine("\n清理前的文件夹结构:");
                DisplayFolderStructure(testProjectPath);

                // 执行清理
                var cleanupResult = await cleanupService.CleanupEmptyFoldersAsync(testProjectPath);
                Console.WriteLine($"\n清理结果: {(cleanupResult.IsSuccess ? "成功" : "失败")}");
                Console.WriteLine($"消息: {cleanupResult.Message}");

                if (cleanupResult.DeletedFolders.Count > 0)
                {
                    Console.WriteLine("已删除的文件夹:");
                    foreach (var folder in cleanupResult.DeletedFolders)
                    {
                        Console.WriteLine($"  - {folder}");
                    }
                }

                Console.WriteLine("\n清理后的文件夹结构:");
                DisplayFolderStructure(testProjectPath);

                // 清理测试项目
                Console.WriteLine($"\n清理测试项目: {testProjectPath}");
                Directory.Delete(testProjectPath, true);

                Console.WriteLine("\n=== 测试完成 ===");
                Console.WriteLine("修复效果验证成功！");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "测试过程中发生错误");
                Console.WriteLine($"错误: {ex.Message}");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
        }

        /// <summary>
        /// 显示文件夹结构
        /// </summary>
        private static void DisplayFolderStructure(string projectPath, string indent = "", int maxDepth = 2, int currentDepth = 0)
        {
            try
            {
                if (currentDepth >= maxDepth)
                    return;

                var directories = Directory.GetDirectories(projectPath);
                foreach (var dir in directories)
                {
                    var dirName = Path.GetFileName(dir);
                    var fileCount = Directory.GetFiles(dir, "*", SearchOption.AllDirectories).Length;
                    var subDirCount = Directory.GetDirectories(dir, "*", SearchOption.AllDirectories).Length;
                    
                    Console.WriteLine($"{indent}📁 {dirName} ({fileCount} 文件, {subDirCount} 子文件夹)");
                    
                    // 递归显示子文件夹
                    if (currentDepth < maxDepth - 1)
                    {
                        DisplayFolderStructure(dir, indent + "  ", maxDepth, currentDepth + 1);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{indent}❌ 无法访问文件夹: {ex.Message}");
            }
        }
    }
}
