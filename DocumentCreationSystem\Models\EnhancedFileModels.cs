using System;
using System.Collections.Generic;

namespace DocumentCreationSystem.Models
{
    /// <summary>
    /// 文件访问模式
    /// </summary>
    public enum FileAccessMode
    {
        /// <summary>
        /// 自动模式 - 根据文件大小和类型智能判断
        /// </summary>
        Auto,
        
        /// <summary>
        /// 完整读取
        /// </summary>
        Full,
        
        /// <summary>
        /// 预览模式 - 只读取开头部分
        /// </summary>
        Preview,
        
        /// <summary>
        /// 摘要模式 - 只生成文件摘要
        /// </summary>
        Summary,
        
        /// <summary>
        /// 智能模式 - 根据内容类型优化读取策略
        /// </summary>
        Smart
    }

    /// <summary>
    /// 文件访问策略
    /// </summary>
    public enum FileAccessStrategy
    {
        /// <summary>
        /// 完整读取
        /// </summary>
        FullRead,
        
        /// <summary>
        /// 部分读取
        /// </summary>
        PartialRead,
        
        /// <summary>
        /// 仅预览
        /// </summary>
        PreviewOnly,
        
        /// <summary>
        /// 仅摘要
        /// </summary>
        SummaryOnly,
        
        /// <summary>
        /// 仅元数据
        /// </summary>
        MetadataOnly
    }

    /// <summary>
    /// 路径信息结果
    /// </summary>
    public class PathInfoResult
    {
        /// <summary>
        /// 目标路径
        /// </summary>
        public string TargetPath { get; set; } = string.Empty;
        
        /// <summary>
        /// 完整路径
        /// </summary>
        public string FullPath { get; set; } = string.Empty;
        
        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// 是否为目录
        /// </summary>
        public bool IsDirectory { get; set; }
        
        /// <summary>
        /// 是否为文件
        /// </summary>
        public bool IsFile { get; set; }
        
        /// <summary>
        /// 是否为文本文件
        /// </summary>
        public bool IsTextFile { get; set; }
        
        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }
        
        /// <summary>
        /// 文件扩展名
        /// </summary>
        public string Extension { get; set; } = string.Empty;
        
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModified { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
        
        /// <summary>
        /// 文件数量（目录）
        /// </summary>
        public int FileCount { get; set; }
        
        /// <summary>
        /// 子目录数量
        /// </summary>
        public int DirectoryCount { get; set; }
        
        /// <summary>
        /// 总大小
        /// </summary>
        public long TotalSize { get; set; }
        
        /// <summary>
        /// 行数（文本文件）
        /// </summary>
        public int LineCount { get; set; }
        
        /// <summary>
        /// 字符数（文本文件）
        /// </summary>
        public int CharacterCount { get; set; }
        
        /// <summary>
        /// 内容预览
        /// </summary>
        public string? ContentPreview { get; set; }
        
        /// <summary>
        /// 子项列表
        /// </summary>
        public List<PathInfoResult>? Children { get; set; }
        
        /// <summary>
        /// 文件类型分布
        /// </summary>
        public Dictionary<string, int>? FileTypeDistribution { get; set; }
        
        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 文件内容结果
    /// </summary>
    public class FileContentResult
    {
        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; } = string.Empty;
        
        /// <summary>
        /// 文件内容
        /// </summary>
        public string? Content { get; set; }
        
        /// <summary>
        /// 是否完整读取
        /// </summary>
        public bool IsComplete { get; set; }
        
        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }
        
        /// <summary>
        /// 文件扩展名
        /// </summary>
        public string Extension { get; set; } = string.Empty;
        
        /// <summary>
        /// 是否为文本文件
        /// </summary>
        public bool IsTextFile { get; set; }
        
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModified { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
        
        /// <summary>
        /// 访问策略
        /// </summary>
        public FileAccessStrategy AccessStrategy { get; set; }
        
        /// <summary>
        /// 总行数
        /// </summary>
        public int? TotalLines { get; set; }
        
        /// <summary>
        /// 预览行数
        /// </summary>
        public int? PreviewLines { get; set; }
        
        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 文件信息项
    /// </summary>
    public class FileInfoItem
    {
        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; } = string.Empty;
        
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; } = string.Empty;
        
        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }
        
        /// <summary>
        /// 文件扩展名
        /// </summary>
        public string Extension { get; set; } = string.Empty;
        
        /// <summary>
        /// 是否为文本文件
        /// </summary>
        public bool IsTextFile { get; set; }
        
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModified { get; set; }
        
        /// <summary>
        /// 文件是否存在
        /// </summary>
        public bool Exists { get; set; }
        
        /// <summary>
        /// 内容预览
        /// </summary>
        public string? ContentPreview { get; set; }
        
        /// <summary>
        /// 访问策略
        /// </summary>
        public FileAccessStrategy? AccessStrategy { get; set; }
        
        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 批量文件信息结果
    /// </summary>
    public class BatchFileInfoResult
    {
        /// <summary>
        /// 文件列表
        /// </summary>
        public List<FileInfoItem> Files { get; set; } = new();
        
        /// <summary>
        /// 总文件数
        /// </summary>
        public int TotalFiles { get; set; }
        
        /// <summary>
        /// 有效文件数
        /// </summary>
        public int ValidFiles { get; set; }
        
        /// <summary>
        /// 文本文件数
        /// </summary>
        public int TextFiles { get; set; }
        
        /// <summary>
        /// 总大小
        /// </summary>
        public long TotalSize { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// 路径搜索选项
    /// </summary>
    public class PathSearchOptions
    {
        /// <summary>
        /// 是否递归搜索
        /// </summary>
        public bool Recursive { get; set; } = true;
        
        /// <summary>
        /// 是否包含文件
        /// </summary>
        public bool IncludeFiles { get; set; } = true;
        
        /// <summary>
        /// 是否包含目录
        /// </summary>
        public bool IncludeDirectories { get; set; } = true;
        
        /// <summary>
        /// 是否包含详细信息
        /// </summary>
        public bool IncludeDetailedInfo { get; set; } = false;
        
        /// <summary>
        /// 是否包含内容预览
        /// </summary>
        public bool IncludeContentPreview { get; set; } = false;
        
        /// <summary>
        /// 最大结果数
        /// </summary>
        public int MaxResults { get; set; } = 100;
    }

    /// <summary>
    /// 路径搜索结果
    /// </summary>
    public class PathSearchResult
    {
        /// <summary>
        /// 基础路径
        /// </summary>
        public string BasePath { get; set; } = string.Empty;
        
        /// <summary>
        /// 搜索模式
        /// </summary>
        public string SearchPattern { get; set; } = string.Empty;
        
        /// <summary>
        /// 匹配的文件
        /// </summary>
        public List<string> MatchedFiles { get; set; } = new();
        
        /// <summary>
        /// 匹配的目录
        /// </summary>
        public List<string> MatchedDirectories { get; set; } = new();
        
        /// <summary>
        /// 总匹配数
        /// </summary>
        public int TotalMatches { get; set; }
        
        /// <summary>
        /// 详细信息
        /// </summary>
        public BatchFileInfoResult? DetailedInfo { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
        
        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }
    }
}
